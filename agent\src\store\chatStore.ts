import { create } from 'zustand';
import { ChatSession, Message, ContentPrompt, GeneratedContent } from '../types';

interface ChatStore {
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  isGenerating: boolean;
  generatedContent: GeneratedContent[];
  
  // Chat actions
  createSession: () => void;
  selectSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  addMessage: (content: string, role: 'user' | 'assistant') => void;
  updateMessage: (messageId: string, updates: Partial<Message>) => void;
  
  // Content generation actions
  generateContent: (prompt: ContentPrompt) => Promise<void>;
  setGenerating: (generating: boolean) => void;
  addGeneratedContent: (content: GeneratedContent) => void;
  updateGeneratedContent: (contentId: string, updates: Partial<GeneratedContent>) => void;
}

export const useChatStore = create<ChatStore>((set, get) => ({
  sessions: [],
  currentSession: null,
  isGenerating: false,
  generatedContent: [],

  createSession: () => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: 'New Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    set(state => ({
      sessions: [newSession, ...state.sessions],
      currentSession: newSession
    }));
  },

  selectSession: (sessionId: string) => {
    const { sessions } = get();
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      set({ currentSession: session });
    }
  },

  deleteSession: (sessionId: string) => {
    set(state => {
      const newSessions = state.sessions.filter(s => s.id !== sessionId);
      const newCurrentSession = state.currentSession?.id === sessionId 
        ? (newSessions[0] || null) 
        : state.currentSession;
      
      return {
        sessions: newSessions,
        currentSession: newCurrentSession
      };
    });
  },

  addMessage: (content: string, role: 'user' | 'assistant') => {
    const { currentSession } = get();
    if (!currentSession) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content,
      role,
      timestamp: new Date(),
      status: role === 'user' ? 'sent' : 'sending'
    };

    set(state => {
      const updatedSessions = state.sessions.map(session => 
        session.id === currentSession.id
          ? {
              ...session,
              messages: [...session.messages, newMessage],
              updatedAt: new Date(),
              title: session.messages.length === 0 ? content.slice(0, 50) + '...' : session.title
            }
          : session
      );

      return {
        sessions: updatedSessions,
        currentSession: {
          ...currentSession,
          messages: [...currentSession.messages, newMessage],
          updatedAt: new Date(),
          title: currentSession.messages.length === 0 ? content.slice(0, 50) + '...' : currentSession.title
        }
      };
    });
  },

  updateMessage: (messageId: string, updates: Partial<Message>) => {
    const { currentSession } = get();
    if (!currentSession) return;

    set(state => {
      const updatedSessions = state.sessions.map(session => 
        session.id === currentSession.id
          ? {
              ...session,
              messages: session.messages.map(msg => 
                msg.id === messageId ? { ...msg, ...updates } : msg
              )
            }
          : session
      );

      return {
        sessions: updatedSessions,
        currentSession: {
          ...currentSession,
          messages: currentSession.messages.map(msg => 
            msg.id === messageId ? { ...msg, ...updates } : msg
          )
        }
      };
    });
  },

  generateContent: async (prompt: ContentPrompt) => {
    set({ isGenerating: true });
    
    try {
      // Simulate content generation
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const generatedContent: GeneratedContent = {
        id: Date.now().toString(),
        prompt,
        script: `Here's your promotional content for "${prompt.text}":\n\n🌟 Transform your hair with our premium hair oil! ✨\n\nDiscover the secret to lustrous, healthy hair that turns heads everywhere you go. Our specially formulated hair oil combines ancient wisdom with modern science to give you:\n\n✅ Deep nourishment and hydration\n✅ Reduced hair fall and breakage\n✅ Enhanced shine and softness\n✅ Faster hair growth\n\nDon't let another day pass with dull, lifeless hair. Join thousands of satisfied customers who've made the switch!\n\n#HairCare #BeautyTips #HealthyHair #HairOil`,
        status: 'ready',
        createdAt: new Date()
      };

      get().addGeneratedContent(generatedContent);
      
      // Add AI response to chat
      get().addMessage(
        `I've generated promotional content for "${prompt.text}"! Here's what I created:\n\n${generatedContent.script}\n\nWould you like me to create a video with voiceover for this content?`,
        'assistant'
      );
      
    } catch (error) {
      console.error('Content generation failed:', error);
      get().addMessage(
        'Sorry, I encountered an error while generating your content. Please try again.',
        'assistant'
      );
    } finally {
      set({ isGenerating: false });
    }
  },

  setGenerating: (generating: boolean) => {
    set({ isGenerating: generating });
  },

  addGeneratedContent: (content: GeneratedContent) => {
    set(state => ({
      generatedContent: [content, ...state.generatedContent]
    }));
  },

  updateGeneratedContent: (contentId: string, updates: Partial<GeneratedContent>) => {
    set(state => ({
      generatedContent: state.generatedContent.map(content =>
        content.id === contentId ? { ...content, ...updates } : content
      )
    }));
  }
}));
