import React from 'react';
import { motion } from 'framer-motion';
import { 
  MessageSquare, 
  Plus, 
  Settings, 
  User, 
  Trash2, 
  Moon, 
  Sun,
  Sparkles,
  Video,
  Share2
} from 'lucide-react';
import { useChatStore } from '../../store/chatStore';
import { useAuthStore } from '../../store/authStore';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  isDark: boolean;
  onThemeToggle: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  isOpen, 
  onToggle, 
  isDark, 
  onThemeToggle 
}) => {
  const { 
    sessions, 
    currentSession, 
    createSession, 
    selectSession, 
    deleteSession 
  } = useChatStore();
  const { user, logout } = useAuthStore();

  const sidebarVariants = {
    open: { x: 0, opacity: 1 },
    closed: { x: -300, opacity: 0 }
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <motion.div
        variants={sidebarVariants}
        animate={isOpen ? 'open' : 'closed'}
        transition={{ type: 'spring', damping: 25, stiffness: 200 }}
        className="fixed left-0 top-0 h-full w-80 bg-white dark:bg-dark-900 border-r border-gray-200 dark:border-dark-700 z-50 lg:relative lg:translate-x-0"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 dark:border-dark-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <h1 className="text-xl font-bold gradient-text">ContentAI</h1>
              </div>
              <button
                onClick={onThemeToggle}
                className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors"
              >
                {isDark ? (
                  <Sun className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                ) : (
                  <Moon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                )}
              </button>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={createSession}
              className="w-full flex items-center space-x-3 px-4 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors"
            >
              <Plus className="w-5 h-5" />
              <span className="font-medium">New Chat</span>
            </motion.button>
          </div>

          {/* Chat Sessions */}
          <div className="flex-1 overflow-y-auto p-4 space-y-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
              Recent Chats
            </h3>
            {sessions.map((session) => (
              <motion.div
                key={session.id}
                whileHover={{ scale: 1.02 }}
                className={`group relative p-3 rounded-lg cursor-pointer transition-all ${
                  currentSession?.id === session.id
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                    : 'hover:bg-gray-100 dark:hover:bg-dark-700 text-gray-700 dark:text-gray-300'
                }`}
                onClick={() => selectSession(session.id)}
              >
                <div className="flex items-start space-x-3">
                  <MessageSquare className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {session.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {session.messages.length} messages
                    </p>
                  </div>
                </div>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteSession(session.id);
                  }}
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/20 text-red-500 transition-all"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              </motion.div>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="p-4 border-t border-gray-200 dark:border-dark-700">
            <div className="space-y-2 mb-4">
              <button className="sidebar-item w-full">
                <Video className="w-5 h-5" />
                <span>Video Library</span>
              </button>
              <button className="sidebar-item w-full">
                <Share2 className="w-5 h-5" />
                <span>Social Accounts</span>
              </button>
              <button className="sidebar-item w-full">
                <Settings className="w-5 h-5" />
                <span>Settings</span>
              </button>
            </div>

            {/* User Profile */}
            <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-dark-800">
              <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {user?.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                  {user?.subscription} plan
                </p>
              </div>
              <button
                onClick={logout}
                className="text-xs text-gray-500 hover:text-red-500 transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </>
  );
};
