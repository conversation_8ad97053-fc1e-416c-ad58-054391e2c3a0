{"name": "agent", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.1", "@mui/lab": "^7.0.0-beta.13", "@mui/material": "^7.1.1", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@tanstack/react-query": "^5.80.7", "@types/three": "^0.177.0", "animejs": "^4.0.2", "axios": "^1.10.0", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "lottie-react": "^2.4.1", "lucide-react": "^0.517.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.1", "three": "^0.177.0", "yup": "^1.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}