import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, Share2, Wand2, Zap, Star } from 'lucide-react';

export const FloatingElements: React.FC = () => {
  const elements = [
    { Icon: Sparkles, color: 'text-primary-400', delay: 0 },
    { Icon: Video, color: 'text-accent-400', delay: 0.5 },
    { Icon: Share2, color: 'text-secondary-400', delay: 1 },
    { Icon: Wand2, color: 'text-primary-500', delay: 1.5 },
    { Icon: Zap, color: 'text-accent-500', delay: 2 },
    { Icon: Star, color: 'text-secondary-500', delay: 2.5 },
  ];

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
      {elements.map(({ Icon, color, delay }, index) => (
        <motion.div
          key={index}
          className={`absolute ${color} opacity-20`}
          initial={{ 
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
            scale: 0,
            rotate: 0
          }}
          animate={{
            y: [null, -100, null],
            x: [null, Math.random() * 100 - 50, null],
            scale: [0, 1, 0],
            rotate: [0, 360, 720],
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            repeat: Infinity,
            delay: delay + Math.random() * 2,
            ease: "easeInOut"
          }}
          style={{
            left: `${10 + Math.random() * 80}%`,
            top: `${10 + Math.random() * 80}%`,
          }}
        >
          <Icon size={24 + Math.random() * 16} />
        </motion.div>
      ))}
      
      {/* Gradient orbs */}
      {[...Array(3)].map((_, index) => (
        <motion.div
          key={`orb-${index}`}
          className="absolute rounded-full blur-xl opacity-10"
          style={{
            background: `linear-gradient(45deg, 
              ${index === 0 ? '#3b82f6' : index === 1 ? '#d946ef' : '#22c55e'}, 
              ${index === 0 ? '#8b5cf6' : index === 1 ? '#f59e0b' : '#06b6d4'})`,
            width: `${200 + Math.random() * 200}px`,
            height: `${200 + Math.random() * 200}px`,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            x: [0, 100, -100, 0],
            y: [0, -100, 100, 0],
            scale: [1, 1.2, 0.8, 1],
          }}
          transition={{
            duration: 15 + Math.random() * 10,
            repeat: Infinity,
            delay: index * 2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};
